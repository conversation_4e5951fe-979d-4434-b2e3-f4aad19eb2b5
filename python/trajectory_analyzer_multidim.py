#!/usr/bin/env python3
"""
高性能多维度轨迹不连续性分析工具
支持每个维度独立配置加速度阈值
分析v2.6文件夹中的所有轨迹数据，筛选不连续轨迹并生成可视化
"""

import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
import os
from pathlib import Path
from multiprocessing import Pool, cpu_count
import time
from functools import partial
import argparse
from typing import List, Tuple, Optional, Union
import warnings
warnings.filterwarnings('ignore')

# 设置matplotlib优化参数
plt.style.use('fast')
plt.rcParams.update({
    'path.simplify': True,
    'path.simplify_threshold': 0.1,
    'agg.path.chunksize': 10000,
    'figure.max_open_warning': 0
})

class MultiDimTrajectoryAnalyzer:
    def __init__(self, 
                 max_acceleration: Union[float, List[float]] = [1000.0, 1000.0, 1000.0, 1000.0], 
                 output_dir: str = "target_data", 
                 plot_dir: str = "target_plots"):
        """
        初始化多维度轨迹分析器
        
        Args:
            max_acceleration: 最大允许加速度阈值，可以是单一值或4维向量
            output_dir: 输出数据目录
            plot_dir: 输出图片目录
        """
        # 处理加速度阈值参数
        if isinstance(max_acceleration, (int, float)):
            self.max_acceleration = [float(max_acceleration)] * 4
        elif isinstance(max_acceleration, (list, tuple)) and len(max_acceleration) == 4:
            self.max_acceleration = [float(x) for x in max_acceleration]
        else:
            raise ValueError("max_acceleration必须是单一数值或长度为4的列表/元组")
        
        self.output_dir = Path(output_dir)
        self.plot_dir = Path(plot_dir)
        self.action_columns = ['action_0', 'action_1', 'action_2', 'action_3']
        
        # 创建输出目录
        self.output_dir.mkdir(exist_ok=True)
        self.plot_dir.mkdir(exist_ok=True)
        
    def load_trajectory_data(self, csv_file: Path) -> Optional[pd.DataFrame]:
        """高效加载轨迹数据"""
        try:
            # 只读取需要的列，使用float32减少内存
            required_columns = ['timestamp'] + self.action_columns
            df = pd.read_csv(csv_file, usecols=required_columns, 
                           dtype={col: np.float32 for col in required_columns})
            
            if df.empty or len(df) < 3:  # 至少需要3个点计算加速度
                return None
                
            return df.sort_values('timestamp').reset_index(drop=True)
            
        except Exception as e:
            print(f"❌ 加载 {csv_file} 失败: {e}")
            return None
    
    def calculate_acceleration(self, df: pd.DataFrame) -> np.ndarray:
        """
        计算4维动作的加速度
        
        Returns:
            4维加速度矩阵 (n_points-2, 4)
        """
        # 计算速度 (一阶差分)
        dt = np.diff(df['timestamp'].values)
        dt = np.where(dt == 0, 1e-6, dt)  # 避免除零
        
        velocities = np.zeros((len(df) - 1, 4))
        for i, col in enumerate(self.action_columns):
            velocities[:, i] = np.diff(df[col].values) / dt
        
        # 计算加速度 (二阶差分)
        if len(velocities) < 2:
            return np.array([]).reshape(0, 4)
            
        dt_acc = dt[1:]  # 加速度计算的时间间隔
        accelerations = np.zeros((len(velocities) - 1, 4))
        
        for i in range(4):
            accelerations[:, i] = np.diff(velocities[:, i]) / dt_acc
        
        return accelerations
    
    def is_discontinuous(self, df: pd.DataFrame) -> Tuple[bool, np.ndarray, List[float], List[bool]]:
        """
        判断轨迹是否不连续
        
        Returns:
            (是否不连续, 加速度矩阵, 每维度最大加速度, 每维度是否超阈值)
        """
        accelerations = self.calculate_acceleration(df)
        
        if len(accelerations) == 0:
            return False, accelerations, [0.0] * 4, [False] * 4
        
        # 计算每个维度的最大绝对加速度
        max_accelerations = [np.max(np.abs(accelerations[:, i])) for i in range(4)]
        
        # 检查每个维度是否超过阈值
        dimension_exceeded = [max_accelerations[i] > self.max_acceleration[i] for i in range(4)]
        
        # 只要有一个维度超过阈值就认为是不连续的
        is_disc = any(dimension_exceeded)
        
        return is_disc, accelerations, max_accelerations, dimension_exceeded
    
    def save_discontinuous_trajectory(self, df: pd.DataFrame, episode_num: int) -> bool:
        """保存不连续轨迹到目标文件夹"""
        try:
            output_file = self.output_dir / f"episode_{episode_num}.csv"
            
            # 只保存需要的列，保持原格式
            output_df = df[['timestamp'] + self.action_columns].copy()
            output_df.to_csv(output_file, index=False, float_format='%.6f')
            
            return True
        except Exception as e:
            print(f"❌ 保存Episode {episode_num}失败: {e}")
            return False
    
    def plot_trajectory(self, df: pd.DataFrame, accelerations: np.ndarray, 
                       episode_num: int, max_accs: List[float], 
                       dimension_exceeded: List[bool]) -> bool:
        """绘制轨迹图"""
        try:
            fig, axes = plt.subplots(3, 2, figsize=(16, 18), dpi=100)
            
            # 绘制4个动作维度
            for i, action_col in enumerate(self.action_columns):
                row, col = i // 2, i % 2
                ax = axes[row, col]
                
                # 根据是否超阈值选择颜色
                color = 'red' if dimension_exceeded[i] else f'C{i}'
                ax.plot(df['timestamp'], df[action_col], 
                       linewidth=1.5, alpha=0.8, color=color)
                
                title = f'{action_col}'
                if dimension_exceeded[i]:
                    title += f' ⚠️ (超阈值: {max_accs[i]:.1f} > {self.max_acceleration[i]:.1f})'
                else:
                    title += f' ✓ (最大: {max_accs[i]:.1f} ≤ {self.max_acceleration[i]:.1f})'
                
                ax.set_title(title, fontsize=11, fontweight='bold')
                ax.set_xlabel('Time (s)', fontsize=10)
                ax.set_ylabel('Action Value', fontsize=10)
                ax.grid(True, alpha=0.3)
                ax.tick_params(labelsize=9)
            
            # 绘制每个维度的加速度
            for i in range(4):
                row = 2
                col = i // 2
                if i >= 2:
                    # 为了显示4个加速度图，我们需要调整布局
                    if i == 2:
                        ax_acc = axes[2, 0]
                    else:  # i == 3
                        ax_acc = axes[2, 1]
                else:
                    continue  # 先只显示前两个维度的加速度
                
                if len(accelerations) > 0:
                    # 加速度对应的时间点（比原始数据少2个点）
                    acc_timestamps = df['timestamp'].iloc[2:].values
                    
                    color = 'red' if dimension_exceeded[i] else f'C{i}'
                    ax_acc.plot(acc_timestamps, np.abs(accelerations[:, i]), 
                               linewidth=1.5, alpha=0.8, color=color, 
                               label=f'{self.action_columns[i]}')
                    ax_acc.axhline(y=self.max_acceleration[i], color='orange', 
                                  linestyle='--', alpha=0.7, 
                                  label=f'阈值: {self.max_acceleration[i]}')
                    ax_acc.legend()
                
                ax_acc.set_title(f'加速度 {self.action_columns[i//2*2:(i//2+1)*2]}', 
                               fontsize=12, fontweight='bold')
                ax_acc.set_xlabel('Time (s)', fontsize=10)
                ax_acc.set_ylabel('|Acceleration|', fontsize=10)
                ax_acc.grid(True, alpha=0.3)
                ax_acc.tick_params(labelsize=9)
            
            # 总标题
            exceeded_dims = [self.action_columns[i] for i, exceeded in enumerate(dimension_exceeded) if exceeded]
            exceeded_str = ', '.join(exceeded_dims) if exceeded_dims else '无'
            
            plt.suptitle(f'Episode {episode_num} - 多维度不连续轨迹分析\n'
                        f'超阈值维度: {exceeded_str}', 
                        fontsize=14, fontweight='bold')
            plt.tight_layout()
            
            # 保存图片
            output_path = self.plot_dir / f'episode_{episode_num}_multidim_discontinuous.png'
            plt.savefig(output_path, bbox_inches='tight', dpi=150, 
                       facecolor='white', edgecolor='none')
            plt.close(fig)
            
            return True
            
        except Exception as e:
            print(f"❌ 绘制Episode {episode_num}失败: {e}")
            return False
    
    def process_single_episode(self, csv_file: Path) -> Optional[dict]:
        """处理单个episode"""
        episode_num = int(csv_file.stem.split('_')[1])
        
        # 加载数据
        df = self.load_trajectory_data(csv_file)
        if df is None:
            return None
        
        # 检查是否不连续
        is_disc, accelerations, max_accs, dimension_exceeded = self.is_discontinuous(df)
        
        if is_disc:
            # 保存数据
            save_success = self.save_discontinuous_trajectory(df, episode_num)
            
            # 绘制图片
            plot_success = self.plot_trajectory(df, accelerations, episode_num, 
                                              max_accs, dimension_exceeded)
            
            return {
                'episode': episode_num,
                'max_accelerations': max_accs,
                'dimension_exceeded': dimension_exceeded,
                'data_points': len(df),
                'save_success': save_success,
                'plot_success': plot_success
            }
        
        return None

    def analyze_all_trajectories(self, data_dir: str = "data/v2.6") -> dict:
        """分析所有轨迹"""
        print("🚀 高性能多维度轨迹不连续性分析工具")
        print("=" * 60)

        # 查找所有CSV文件
        data_path = Path(data_dir)
        csv_files = list(data_path.glob('episode_*.csv'))

        if not csv_files:
            print(f"❌ 在 {data_dir} 中未找到episode_*.csv文件")
            return {}

        csv_files.sort(key=lambda x: int(x.stem.split('_')[1]))
        print(f"📁 找到 {len(csv_files)} 个CSV文件")
        print(f"🎯 多维度加速度阈值: {self.max_acceleration}")
        print(f"📂 输出目录: {self.output_dir}")
        print(f"🎨 图片目录: {self.plot_dir}")

        # 并行处理
        start_time = time.time()
        max_processes = min(cpu_count(), len(csv_files))

        print(f"🔄 使用 {max_processes} 个进程并行处理...")

        with Pool(processes=max_processes) as pool:
            results = pool.map(self.process_single_episode, csv_files)

        # 统计结果
        discontinuous_episodes = [r for r in results if r is not None]

        elapsed = time.time() - start_time

        print("\n" + "=" * 60)
        print("📊 分析结果:")
        print(f"  总文件数: {len(csv_files)}")
        print(f"  不连续轨迹数: {len(discontinuous_episodes)}")
        print(f"  连续轨迹数: {len(csv_files) - len(discontinuous_episodes)}")
        print(f"  不连续率: {len(discontinuous_episodes)/len(csv_files)*100:.1f}%")

        if discontinuous_episodes:
            # 统计每个维度的超阈值情况
            dim_exceeded_count = [0, 0, 0, 0]
            for episode in discontinuous_episodes:
                for i, exceeded in enumerate(episode['dimension_exceeded']):
                    if exceeded:
                        dim_exceeded_count[i] += 1

            print(f"  各维度超阈值统计:")
            for i, count in enumerate(dim_exceeded_count):
                print(f"    {self.action_columns[i]}: {count}/{len(discontinuous_episodes)} "
                      f"({count/len(discontinuous_episodes)*100:.1f}%)")

            save_success = sum(r['save_success'] for r in discontinuous_episodes)
            plot_success = sum(r['plot_success'] for r in discontinuous_episodes)
            print(f"  数据保存成功: {save_success}/{len(discontinuous_episodes)}")
            print(f"  图片生成成功: {plot_success}/{len(discontinuous_episodes)}")

        print(f"⏱️  总处理时间: {elapsed:.2f}s")
        print(f"📈 平均处理速度: {len(csv_files)/elapsed:.1f} files/s")

        return {
            'total_files': len(csv_files),
            'discontinuous_count': len(discontinuous_episodes),
            'discontinuous_episodes': discontinuous_episodes,
            'processing_time': elapsed,
            'dimension_thresholds': self.max_acceleration
        }


def main():
    """主函数 - 合并了run_analysis.py的功能"""
    parser = argparse.ArgumentParser(description='多维度轨迹不连续性分析工具')
    parser.add_argument('--max_acceleration', nargs='+', type=float,
                       default=[1000.0, 1000.0, 1000.0, 1000.0],
                       help='4维加速度阈值 (默认: 1000.0 1000.0 1000.0 1000.0)')
    parser.add_argument('--data_dir', type=str, default='data/v2.6',
                       help='输入数据目录 (默认: data/v2.6)')
    parser.add_argument('--output_dir', type=str, default='target_data',
                       help='输出数据目录 (默认: target_data)')
    parser.add_argument('--plot_dir', type=str, default='target_plots',
                       help='输出图片目录 (默认: target_plots)')

    args = parser.parse_args()

    # 验证加速度阈值参数
    if len(args.max_acceleration) == 1:
        # 如果只提供一个值，应用到所有维度
        max_acc = [args.max_acceleration[0]] * 4
    elif len(args.max_acceleration) == 4:
        max_acc = args.max_acceleration
    else:
        print("❌ max_acceleration必须是1个值或4个值")
        return

    print("🚀 开始多维度轨迹分析...")
    print(f"🎯 使用加速度阈值: {max_acc}")

    # 创建分析器
    analyzer = MultiDimTrajectoryAnalyzer(
        max_acceleration=max_acc,
        output_dir=args.output_dir,
        plot_dir=args.plot_dir
    )

    # 执行分析
    results = analyzer.analyze_all_trajectories(args.data_dir)

    if results:
        print(f"\n🎉 分析完成！")
        if results['discontinuous_count'] > 0:
            print(f"✅ 发现 {results['discontinuous_count']} 个不连续轨迹")
            print(f"📁 数据已保存到 {args.output_dir}/ 目录")
            print(f"🎨 图片已保存到 {args.plot_dir}/ 目录")
        else:
            print("📊 所有轨迹都是连续的（在给定阈值下）")


# 快速运行函数（无需命令行参数）
def quick_run(max_acceleration=[1000.0, 1000.0, 1000.0, 1000.0]):
    """快速运行函数，用于简单调用"""
    print("🚀 开始多维度轨迹分析...")
    print(f"🎯 使用加速度阈值: {max_acceleration}")

    # 创建分析器
    analyzer = MultiDimTrajectoryAnalyzer(
        max_acceleration=max_acceleration,
        output_dir="target_data_multidim",
        plot_dir="target_plots_multidim"
    )

    # 执行分析
    results = analyzer.analyze_all_trajectories("data/v2.6")

    if results:
        print(f"\n🎉 分析完成！")
        if results['discontinuous_count'] > 0:
            print(f"✅ 发现 {results['discontinuous_count']} 个不连续轨迹")
            print(f"📁 数据已保存到 target_data_multidim/ 目录")
            print(f"🎨 图片已保存到 target_plots_multidim/ 目录")
        else:
            print("📊 所有轨迹都是连续的（在给定阈值下）")

    return results


if __name__ == "__main__":
    main()
