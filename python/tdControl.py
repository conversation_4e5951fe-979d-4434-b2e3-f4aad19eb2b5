import pygame
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation
from collections import deque

# 定义 fhan 函数
def fhan(x1, x2, r, h):
    """
    快速跟踪非线性函数 (fhan)
    :param x1: 状态变量1 (跟踪误差)
    :param x2: 状态变量2 (微分信号)
    :param r: 控制收敛速度的参数
    :param h: 控制平滑度的参数
    :return: 非线性函数输出
    """
    d = r * h**2
    a0 = h * x2
    y = x1 + a0
    a1 = np.sqrt(d * (d + 8 * np.abs(y)))
    a2 = a0 + np.sign(y) * (a1 - d) / 2
    sy = (np.sign(y + d) - np.sign(y - d)) / 2
    a = (a0 + y - a2) * sy + a2
    sa = (np.sign(a + d) - np.sign(a - d)) / 2
    return -r * (a / d - np.sign(a)) * sa - r * np.sign(a)

# 定义非线性跟踪微分器 (NTD)
class NonlinearTrackingDifferentiator:
    def __init__(self, r, h, dt):
        """
        初始化 NTD
        :param r: 控制收敛速度的参数
        :param h: 控制平滑度的参数
        :param dt: 时间步长
        """
        self.r = r
        self.h = h
        self.dt = dt
        self.x1 = 0  # 跟踪信号
        self.x2 = 0  # 微分信号

    def update(self, u):
        """
        更新 NTD 状态
        :param u: 输入信号
        :return: 跟踪信号 x1 和微分信号 x2
        """
        dx1 = self.x2
        dx2 = fhan(self.x1 - u, self.x2, self.r, self.h)
        self.x1 += dx1 * self.dt
        self.x2 += dx2 * self.dt
        return self.x1, self.x2

# 初始化 pygame 和手柄
pygame.init()
pygame.joystick.init()
joystick = pygame.joystick.Joystick(0)
joystick.init()

# 初始化 NTD
r = 20  # 控制收敛速度 200 
h = 0.01  # 控制平滑度  大加快响应
dt = 0.005  # 时间步长   
ntd = NonlinearTrackingDifferentiator(r, h, dt)

# 初始化数据存储，使用 deque 并设置最大长度
t_values = deque(maxlen=1000)
target_values = deque(maxlen=1000)
x1_values = deque(maxlen=1000)
x2_values = deque(maxlen=1000)

# 初始化绘图
fig, ax = plt.subplots(figsize=(10, 6))
ax.set_title("Nonlinear Tracking Differentiator")
ax.set_ylim(-1.5, 1.5)
ax.grid()

# 初始化绘图线条，设置颜色
line_target, = ax.plot([], [], label="Target", color="blue")
line_x1, = ax.plot([], [], label="Result (x1)", color="red")
line_x2, = ax.plot([], [], label="Diff (x2)", color="green")
ax.legend()

# 更新函数
def update(frame):
    # 读取手柄输入
    pygame.event.pump()
    target = joystick.get_axis(0)
    
    # 更新 NTD
    x1, x2 = ntd.update(target)
    
    # 记录数据
    t = frame * dt
    t_values.append(t)
    target_values.append(target)
    x1_values.append(x1)
    x2_values.append(x2)
    
    # 设置横轴范围为 max(0, t - 7.5) 到 t + 2.5
    ax.set_xlim(max(0, t - 7.5), t + 2.5)
    
    # 更新绘图数据
    line_target.set_data(t_values, target_values)
    line_x1.set_data(t_values, x1_values)
    #line_x2.set_data(t_values, x2_values)
    
    return line_target, line_x1#, line_x2

# 运行动画，设置 frames=None 以无限循环
ani = FuncAnimation(fig, update, frames=None, interval=1, blit=True)
plt.show()